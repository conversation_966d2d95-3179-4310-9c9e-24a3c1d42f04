$(document).ready(function() {
    // Export functionality
    $('.export-application-button').on('click', function(e) {
        e.preventDefault();
        
        // Get current filters
        var filters = {};
        $('.dataTables_wrapper .form-control').each(function() {
            var name = $(this).attr('name');
            var value = $(this).val();
            if (name && value) {
                filters[name] = value;
            }
        });
        
        // Build export URL with filters
        var exportUrl = $(this).data('url') || '/admin/recruitment-applications/export';
        var queryString = $.param(filters);
        if (queryString) {
            exportUrl += '?' + queryString;
        }
        
        // Trigger download
        window.location.href = exportUrl;
    });
    
    // Status change handling
    $('.status-select').on('change', function() {
        var applicationId = $(this).data('id');
        var newStatus = $(this).val();
        var $this = $(this);
        
        if (applicationId && newStatus) {
            $.ajax({
                url: '/admin/recruitment-applications/' + applicationId + '/update-status',
                method: 'POST',
                data: {
                    status: newStatus,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.error) {
                        alert('Error: ' + response.message);
                        // Revert select value
                        $this.val($this.data('original-value'));
                    } else {
                        // Update success
                        $this.data('original-value', newStatus);
                        if (response.message) {
                            // Show success message
                            console.log('Status updated successfully');
                        }
                    }
                },
                error: function() {
                    alert('An error occurred while updating status');
                    // Revert select value
                    $this.val($this.data('original-value'));
                }
            });
        }
    });
    
    // Store original values for status selects
    $('.status-select').each(function() {
        $(this).data('original-value', $(this).val());
    });
    
    // CV download tracking
    $('.cv-download-btn').on('click', function() {
        var applicationId = $(this).data('application-id');
        if (applicationId) {
            // Track download event
            console.log('CV downloaded for application:', applicationId);
        }
    });
});

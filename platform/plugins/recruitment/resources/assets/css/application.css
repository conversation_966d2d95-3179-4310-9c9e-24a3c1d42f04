/* Recruitment Application Styles */

.recruitment-application-table {
    width: 100%;
}

.recruitment-application-table .btn-group {
    display: flex;
    gap: 5px;
}

.recruitment-application-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-new {
    background-color: #3b82f6;
    color: white;
}

.status-processing {
    background-color: #f59e0b;
    color: white;
}

.status-passed {
    background-color: #10b981;
    color: white;
}

.status-failed {
    background-color: #ef4444;
    color: white;
}

/* Export button styles */
.export-application-button {
    margin-left: 10px;
}

/* Form styles */
.recruitment-form .form-group {
    margin-bottom: 1rem;
}

.recruitment-form .form-control {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.recruitment-form .form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive styles */
@media (max-width: 768px) {
    .recruitment-application-table .btn-group {
        flex-direction: column;
    }
    
    .recruitment-application-table .btn-sm {
        margin-bottom: 2px;
    }
}

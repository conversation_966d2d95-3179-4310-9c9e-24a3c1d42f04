<?php

namespace Botble\Recruitment\Providers;

use Botble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Facades\EmailHandler;
use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Setting\PanelSections\SettingOthersPanelSection;
use Bo<PERSON>ble\Setting\Supports\SettingStore;
use Botble\Base\Facades\PanelSectionManager;
use Botble\Base\Models\PanelSectionItem;
use Botble\Language\Facades\Language;
use Botble\Recruitment\Models\Recruitment;
use Botble\Recruitment\Models\RecruitmentCategory;
use Botble\Recruitment\Models\RecruitmentTag;
use Botble\Recruitment\Models\WorkType;
use Botble\Recruitment\Models\Province;
use Botble\Recruitment\Models\District;
use Botble\Slug\Facades\SlugHelper;
use Illuminate\Routing\Events\RouteMatched;
use Botble\Captcha\Facades\Captcha;
use \Botble\Recruitment\Forms\Fronts\RecruitmentApplicationForm;
use \Botble\Recruitment\Http\Requests\RecruitmentApplicationRequest;


class RecruitmentServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->register(ShortcodeProvider::class);
    }

    public function boot(): void
    {

        // Register slug
        SlugHelper::registerModule(Recruitment::class, 'Recruitment');
        SlugHelper::registerModule(RecruitmentCategory::class, 'Recruitment Categories');
        SlugHelper::registerModule(RecruitmentTag::class, 'Recruitment Tags');
        SlugHelper::registerModule(WorkType::class, 'Work Types');
        SlugHelper::registerModule(Province::class, 'Provinces');
        SlugHelper::registerModule(District::class, 'Districts');


        if (is_plugin_active('captcha')) {
            Captcha::registerFormSupport(RecruitmentApplicationForm::class, RecruitmentApplicationRequest::class, 'Form Application');
        }


        $this
            ->setNamespace('plugins/recruitment')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions', 'general', 'email'])
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->loadMigrations()
            ->publishAssets();

        // Register language support
        if (defined('LANGUAGE_MODULE_SCREEN_NAME')) {
            Language::registerModule([Recruitment::class, RecruitmentCategory::class, RecruitmentTag::class, WorkType::class, Province::class, District::class]);
        }

        // Register settings panel
        PanelSectionManager::default()->beforeRendering(function (): void {
            PanelSectionManager::registerItem(
                SettingOthersPanelSection::class,
                fn () => PanelSectionItem::make('recruitment')
                    ->setTitle(trans('plugins/recruitment::recruitment.settings.title'))
                    ->withIcon('ti ti-briefcase')
                    ->withPriority(150)
                    ->withDescription(trans('plugins/recruitment::recruitment.settings.description'))
                    ->withRoute('recruitment.settings')
            );
        });

        $this->app->booted(function (): void {
            EmailHandler::addTemplateSettings(RECRUITMENT_MODULE_SCREEN_NAME, config('plugins.recruitment.email', []));

            $this->app->register(HookRecruitmentProvider::class);
        });

        $this->app['events']->listen(RouteMatched::class, function () {
            // Add JavaScript for admin
            if (request()->is(config('core.base.general.admin_dir') . '/*')) {
                Assets::addScriptsDirectly('vendor/core/plugins/recruitment/js/recruitment-admin.js');
                Assets::addStylesDirectly('vendor/core/plugins/recruitment/css/application.css');

                // Register recruitment-applications script
                Assets::addScriptsDirectly([
                        'vendor/core/plugins/recruitment/js/recruitment-applications.js'
                    ]);
            }

            // Register dashboard menu
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-recruitment',
                    'priority' => 5,
                    'parent_id' => null,
                    'name' => 'plugins/recruitment::recruitment.name',
                    'icon' => 'fa fa-briefcase',
                    'url' => route('recruitment.index'),
                    'permissions' => ['recruitment.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-list',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.list',
                    'icon' => null,
                    'url' => route('recruitment.index'),
                    'permissions' => ['recruitment.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-applications',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.applications',
                    'icon' => null,
                    'url' => route('recruitment-applications.index'),
                    'permissions' => ['recruitment-applications.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-categories',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.categories',
                    'icon' => null,
                    'url' => route('recruitment-categories.index'),
                    'permissions' => ['recruitment-categories.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-tags',
                    'priority' => 4,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.tags',
                    'icon' => null,
                    'url' => route('recruitment-tags.index'),
                    'permissions' => ['recruitment-tags.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-work-types',
                    'priority' => 5,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.work_types',
                    'icon' => null,
                    'url' => route('recruitment-work-types.index'),
                    'permissions' => ['recruitment-work-types.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-provinces',
                    'priority' => 6,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.provinces',
                    'icon' => null,
                    'url' => route('recruitment-provinces.index'),
                    'permissions' => ['recruitment-provinces.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-recruitment-districts',
                    'priority' => 7,
                    'parent_id' => 'cms-plugins-recruitment',
                    'name' => 'plugins/recruitment::recruitment.districts',
                    'icon' => null,
                    'url' => route('recruitment-districts.index'),
                    'permissions' => ['recruitment-districts.index'],
                ]);
        });
    }
}

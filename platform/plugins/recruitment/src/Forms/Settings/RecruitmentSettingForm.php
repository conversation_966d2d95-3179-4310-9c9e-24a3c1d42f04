<?php

namespace Bo<PERSON><PERSON>\Recruitment\Forms\Settings;

use Bo<PERSON>ble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\OnOffCheckboxField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Recruitment\Http\Requests\Settings\RecruitmentSettingRequest;
use Botble\Setting\Forms\SettingForm;

class RecruitmentSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        Assets::addStylesDirectly('vendor/core/core/base/libraries/tagify/tagify.css')
            ->addScriptsDirectly([
                'vendor/core/core/base/libraries/tagify/tagify.js',
                'vendor/core/core/base/js/tags.js',
            ]);

        $this
            ->setSectionTitle(trans('plugins/recruitment::recruitment.settings.title'))
            ->setSectionDescription(trans('plugins/recruitment::recruitment.settings.description'))
            ->setValidatorClass(RecruitmentSettingRequest::class)
            ->add(
                'recruitment_receiver_emails',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->addAttribute('class', 'tags form-control')
                    ->value(setting('recruitment_receiver_emails'))
                    ->label(trans('plugins/recruitment::recruitment.settings.receiver_emails'))
                    ->placeholder(trans('plugins/recruitment::recruitment.settings.receiver_emails_placeholder'))
                    ->helperText(trans('plugins/recruitment::recruitment.settings.receiver_emails_helper'))
            )
            ->add(
                'recruitment_enable_confirmation_email',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->defaultValue((bool) setting('recruitment_enable_confirmation_email', true))
                    ->label(trans('plugins/recruitment::recruitment.settings.enable_confirmation_email'))
                    ->helperText(trans('plugins/recruitment::recruitment.settings.enable_confirmation_email_helper'))
            );
    }
}

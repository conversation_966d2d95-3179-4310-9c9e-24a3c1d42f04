<?php

namespace Bo<PERSON>ble\Recruitment\Http\Requests\Settings;

use Bo<PERSON>ble\Base\Rules\EmailRule;
use Bo<PERSON>ble\Support\Http\Requests\Request;

class RecruitmentSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            'recruitment_receiver_emails' => 'nullable|string',
            'recruitment_receiver_emails_for_validation' => 'nullable|array',
            'recruitment_receiver_emails_for_validation.*' => ['required', new EmailRule()],
            'recruitment_enable_confirmation_email' => 'nullable|in:0,1',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'recruitment_receiver_emails_for_validation' => $this->parseTagInputToArray('recruitment_receiver_emails'),
        ]);
    }

    protected function parseTagInputToArray(string $name): array
    {
        $data = $this->input($name);
        $data = is_string($data) ? trim($data) : '';

        if (! $data) {
            return [];
        }

        $data = collect(json_decode($data, true))
            ->pluck('value')
            ->all();

        if (! $data) {
            return [];
        }

        return $data;
    }

    public function attributes(): array
    {
        return [
            'recruitment_receiver_emails_for_validation.*' => trans('plugins/recruitment::recruitment.settings.receiver_emails'),
            'recruitment_enable_confirmation_email' => trans('plugins/recruitment::recruitment.settings.enable_confirmation_email'),
        ];
    }
}

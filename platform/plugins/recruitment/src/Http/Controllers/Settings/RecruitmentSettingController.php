<?php

namespace Bo<PERSON>ble\Recruitment\Http\Controllers\Settings;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Recruitment\Forms\Settings\RecruitmentSettingForm;
use Bo<PERSON>ble\Recruitment\Http\Requests\Settings\RecruitmentSettingRequest;
use Botble\Setting\Http\Controllers\SettingController;
use Illuminate\Support\Arr;

class RecruitmentSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('plugins/recruitment::recruitment.settings.title'));

        return RecruitmentSettingForm::create()->renderForm();
    }

    public function update(RecruitmentSettingRequest $request): BaseHttpResponse
    {
        return $this->performUpdate(Arr::except($request->validated(), [
            'recruitment_receiver_emails_for_validation',
        ]));
    }
}
